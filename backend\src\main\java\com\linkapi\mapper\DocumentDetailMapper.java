package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.DocumentDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 单据明细Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface DocumentDetailMapper extends BaseMapper<DocumentDetail> {

    /**
     * 根据单据ID查询明细
     */
    @Select("SELECT * FROM document_details WHERE is_deleted = 0 AND document_id = #{documentId} ORDER BY line_no")
    List<DocumentDetail> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据商品编码统计明细数量
     */
    @Select("SELECT product_code, COUNT(*) as count, SUM(quantity) as total_quantity, SUM(amount) as total_amount " +
            "FROM document_details WHERE is_deleted = 0 GROUP BY product_code ORDER BY count DESC")
    List<Map<String, Object>> countByProductCode();

    /**
     * 查询热销商品TOP10
     */
    @Select("SELECT product_code, product_name, SUM(quantity) as total_quantity, SUM(amount) as total_amount, COUNT(*) as order_count " +
            "FROM document_details dd " +
            "INNER JOIN documents d ON dd.document_id = d.id " +
            "WHERE dd.is_deleted = 0 AND d.is_deleted = 0 AND d.document_type = 'SALE_OUT' " +
            "GROUP BY product_code, product_name " +
            "ORDER BY total_quantity DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectTopSellingProducts();

    /**
     * 批量删除单据明细
     */
    @Select("UPDATE document_details SET is_deleted = 1, updated_time = NOW() WHERE document_id = #{documentId}")
    int deleteByDocumentId(@Param("documentId") Long documentId);

    /**
     * 查询商品销售统计
     */
    @Select("SELECT " +
            "product_code, " +
            "product_name, " +
            "COUNT(*) as order_count, " +
            "SUM(quantity) as total_quantity, " +
            "SUM(amount) as total_amount, " +
            "AVG(unit_price) as avg_price " +
            "FROM document_details dd " +
            "INNER JOIN documents d ON dd.document_id = d.id " +
            "WHERE dd.is_deleted = 0 AND d.is_deleted = 0 " +
            "AND d.document_type = 'SALE_OUT' " +
            "AND d.created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY product_code, product_name " +
            "ORDER BY total_amount DESC")
    List<Map<String, Object>> selectProductSalesStatistics(@Param("startTime") String startTime, @Param("endTime") String endTime);
}