package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.Document;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 单据Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface DocumentMapper extends BaseMapper<Document> {

    /**
     * 根据状态统计单据数量
     */
    @Select("SELECT status, COUNT(*) as count FROM documents WHERE is_deleted = 0 GROUP BY status")
    List<Map<String, Object>> countByStatus();

    /**
     * 根据单据类型统计数量
     */
    @Select("SELECT document_type, COUNT(*) as count FROM documents WHERE is_deleted = 0 GROUP BY document_type")
    List<Map<String, Object>> countByDocumentType();

    /**
     * 查询指定时间范围内的单据
     */
    @Select("SELECT * FROM documents WHERE is_deleted = 0 AND created_time BETWEEN #{startTime} AND #{endTime} ORDER BY created_time DESC")
    List<Document> selectByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询待推送的单据
     */
    @Select("SELECT * FROM documents WHERE is_deleted = 0 AND status = 'VALIDATED' ORDER BY created_time ASC LIMIT #{limit}")
    List<Document> selectPendingPushDocuments(@Param("limit") int limit);

    /**
     * 查询失败重试的单据
     */
    @Select("SELECT * FROM documents WHERE is_deleted = 0 AND status = 'FAILED' AND retry_count < #{maxRetry} ORDER BY last_push_time ASC LIMIT #{limit}")
    List<Document> selectRetryDocuments(@Param("maxRetry") int maxRetry, @Param("limit") int limit);

    /**
     * 更新单据推送状态
     */
    @Select("UPDATE documents SET status = #{status}, kingdee_document_no = #{kingdeeDocumentNo}, error_message = #{errorMessage}, retry_count = #{retryCount}, last_push_time = #{lastPushTime}, updated_time = NOW() WHERE id = #{id}")
    int updatePushStatus(@Param("id") Long id, 
                        @Param("status") String status,
                        @Param("kingdeeDocumentNo") String kingdeeDocumentNo,
                        @Param("errorMessage") String errorMessage,
                        @Param("retryCount") Integer retryCount,
                        @Param("lastPushTime") LocalDateTime lastPushTime);

    /**
     * 查询单据概览统计
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_count, " +
            "SUM(CASE WHEN status = 'VALIDATED' THEN 1 ELSE 0 END) as validated_count, " +
            "SUM(CASE WHEN status = 'PUSHING' THEN 1 ELSE 0 END) as pushing_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "SUM(total_amount) as total_amount " +
            "FROM documents WHERE is_deleted = 0")
    Map<String, Object> selectDocumentSummary();

    /**
     * 查询今日单据统计
     */
    @Select("SELECT " +
            "COUNT(*) as today_count, " +
            "SUM(total_amount) as today_amount " +
            "FROM documents WHERE is_deleted = 0 AND DATE(created_time) = CURDATE()")
    Map<String, Object> selectTodayStatistics();

    /**
     * 查询最近7天单据趋势
     */
    @Select("SELECT " +
            "DATE(created_time) as date, " +
            "COUNT(*) as count, " +
            "SUM(total_amount) as amount " +
            "FROM documents " +
            "WHERE is_deleted = 0 AND created_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
            "GROUP BY DATE(created_time) " +
            "ORDER BY date")
    List<Map<String, Object>> selectWeeklyTrend();

    /**
     * 查询重复单据
     */
    @Select("SELECT document_no, document_type, COUNT(*) as count " +
            "FROM documents " +
            "WHERE is_deleted = 0 " +
            "GROUP BY document_no, document_type " +
            "HAVING COUNT(*) > 1")
    List<Map<String, Object>> selectDuplicateDocuments();

    /**
     * 根据来源文件查询单据
     */
    @Select("SELECT * FROM documents WHERE is_deleted = 0 AND source_file = #{sourceFile} ORDER BY created_time DESC")
    List<Document> selectBySourceFile(@Param("sourceFile") String sourceFile);
}