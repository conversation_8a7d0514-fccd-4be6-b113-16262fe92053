package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.ProductMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 商品映射Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface ProductMappingMapper extends BaseMapper<ProductMapping> {

    /**
     * 根据外部编码查询映射
     */
    @Select("SELECT * FROM product_mapping WHERE is_deleted = 0 AND external_code = #{externalCode} AND status = 'ACTIVE'")
    ProductMapping selectByExternalCode(@Param("externalCode") String externalCode);

    /**
     * 根据金蝶编码查询映射
     */
    @Select("SELECT * FROM product_mapping WHERE is_deleted = 0 AND kingdee_code = #{kingdeeCode} AND status = 'ACTIVE'")
    ProductMapping selectByKingdeeCode(@Param("kingdeeCode") String kingdeeCode);

    /**
     * 查询所有有效映射
     */
    @Select("SELECT * FROM product_mapping WHERE is_deleted = 0 AND status = 'ACTIVE' ORDER BY created_time DESC")
    List<ProductMapping> selectActiveMapping();

    /**
     * 批量插入映射
     */
    int batchInsert(@Param("list") List<ProductMapping> mappings);

    /**
     * 统计映射数量
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count, " +
            "SUM(CASE WHEN status = 'INACTIVE' THEN 1 ELSE 0 END) as inactive_count " +
            "FROM product_mapping WHERE is_deleted = 0")
    Map<String, Object> selectMappingStatistics();

    /**
     * 查询未映射的商品
     */
    @Select("SELECT DISTINCT dd.product_code, dd.product_name " +
            "FROM document_details dd " +
            "LEFT JOIN product_mapping pm ON dd.product_code = pm.external_code AND pm.is_deleted = 0 " +
            "WHERE dd.is_deleted = 0 AND pm.id IS NULL " +
            "ORDER BY dd.product_code")
    List<Map<String, Object>> selectUnmappedProducts();

    /**
     * 根据关键词搜索映射
     */
    @Select("SELECT * FROM product_mapping " +
            "WHERE is_deleted = 0 " +
            "AND (external_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR external_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_time DESC")
    List<ProductMapping> searchMapping(@Param("keyword") String keyword);
}