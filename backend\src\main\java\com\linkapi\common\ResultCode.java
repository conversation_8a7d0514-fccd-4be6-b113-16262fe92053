package com.linkapi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> API Team
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 文件相关状态码
    FILE_UPLOAD_ERROR(1001, "文件上传失败"),
    FILE_NOT_FOUND(1002, "文件不存在"),
    FILE_FORMAT_ERROR(1003, "文件格式错误"),
    FILE_SIZE_EXCEEDED(1004, "文件大小超出限制"),
    FILE_PARSE_ERROR(1005, "文件解析失败"),

    // Excel相关状态码
    EXCEL_READ_ERROR(1101, "Excel读取失败"),
    EXCEL_FORMAT_ERROR(1102, "Excel格式错误"),
    EXCEL_EMPTY_ERROR(1103, "Excel文件为空"),
    EXCEL_HEADER_ERROR(1104, "Excel表头格式错误"),
    EXCEL_DATA_ERROR(1105, "Excel数据格式错误"),

    // 数据校验相关状态码
    DATA_VALIDATION_ERROR(2001, "数据校验失败"),
    REQUIRED_FIELD_MISSING(2002, "必填字段缺失"),
    FIELD_FORMAT_ERROR(2003, "字段格式错误"),
    AMOUNT_CALCULATION_ERROR(2004, "金额计算错误"),
    DUPLICATE_DOCUMENT_ERROR(2005, "单据重复"),
    DOCUMENT_NOT_FOUND(2006, "原单据不存在"),
    RETURN_DOCUMENT_ERROR(2007, "退货单据错误"),

    // 基础资料相关状态码
    MASTER_DATA_NOT_FOUND(3001, "基础资料不存在"),
    PRODUCT_NOT_FOUND(3002, "商品不存在"),
    CUSTOMER_NOT_FOUND(3003, "客户不存在"),
    WAREHOUSE_NOT_FOUND(3004, "仓库不存在"),
    MAPPING_NOT_FOUND(3005, "映射关系不存在"),
    MASTER_DATA_SYNC_ERROR(3006, "基础资料同步失败"),
    DATA_NOT_FOUND(3007, "数据不存在"),
    DATA_ALREADY_EXISTS(3008, "数据已存在"),

    // 金蝶API相关状态码
    KINGDEE_API_ERROR(4001, "金蝶API调用失败"),
    KINGDEE_AUTH_ERROR(4002, "金蝶认证失败"),
    KINGDEE_PARAM_ERROR(4003, "金蝶API参数错误"),
    KINGDEE_BUSINESS_ERROR(4004, "金蝶业务处理失败"),
    KINGDEE_NETWORK_ERROR(4005, "金蝶网络连接失败"),
    KINGDEE_TIMEOUT_ERROR(4006, "金蝶API调用超时"),

    // 单据处理相关状态码
    DOCUMENT_PROCESS_ERROR(5001, "单据处理失败"),
    DOCUMENT_STATUS_ERROR(5002, "单据状态错误"),
    DOCUMENT_TYPE_ERROR(5003, "单据类型错误"),
    DOCUMENT_PUSH_ERROR(5004, "单据推送失败"),
    DOCUMENT_RETRY_EXCEEDED(5005, "单据重试次数超限"),

    // 系统相关状态码
    SYSTEM_ERROR(9001, "系统内部错误"),
    DATABASE_ERROR(9002, "数据库操作失败"),
    NETWORK_ERROR(9003, "网络连接失败"),
    CONFIG_ERROR(9004, "配置错误"),
    SERVICE_UNAVAILABLE(9005, "服务不可用"),
    BUSINESS_ERROR(9006, "业务处理错误");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;
}